import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionJobData, AutoActionJobOptions } from './dto/auto-action-job.dto';

@Injectable()
export class AutoActionQueue {
  private readonly logger = new Logger(AutoActionQueue.name);

  constructor(
    @InjectQueue('auto-actions')
    private readonly autoActionQueue: Queue<AutoActionJobData>,
  ) {}

  /**
   * Add a new auto action job to the queue
   */
  async addAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions: AutoActionJobOptions = {
      repeat: {
        pattern: '*/1 * * * *', // Every 30 minutes
      },
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
      attempts: 3, // Retry up to 3 times on failure
      backoff: {
        type: 'exponential',
        delay: 2000, // Start with 2 second delay
      },
    };

    try {
      await this.autoActionQueue.add(jobName, jobData, jobOptions);
      this.logger.log(`Added auto action job: ${jobName}`);
    } catch (error) {
      this.logger.error(`Failed to add auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Add a one-time retry job with delay
   */
  async addRetryJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs: number,
  ): Promise<void> {
    const retryJobName = `${userId}_${targetId}_${type}_retry_${Date.now()}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions = {
      delay: delayMs, // Delay before execution
      removeOnComplete: 5, // Keep fewer completed retry jobs
      removeOnFail: 10, // Keep fewer failed retry jobs
      attempts: 1, // Don't retry the retry job
    };

    try {
      await this.autoActionQueue.add(retryJobName, jobData, jobOptions);
      this.logger.log(`Added retry job: ${retryJobName} with ${delayMs}ms delay`);
    } catch (error) {
      this.logger.error(`Failed to add retry job: ${retryJobName}`, error);
      throw error;
    }
  }

  /**
   * Remove an auto action job from the queue
   */
  async removeAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // First, try to remove repeatable job pattern
      let removed = false;
      try {
        removed = await this.autoActionQueue.removeRepeatable(jobName, {
          pattern: '*/30 * * * *',
        });
        this.logger.log(`Removed repeatable job pattern: ${jobName}, success: ${removed}`);
      } catch (removeError) {
        this.logger.warn(`Failed to remove repeatable pattern for ${jobName}: ${removeError.message}`);
      }

      // If the repeatable pattern removal failed, try alternative approaches
      if (!removed) {
        this.logger.log(`Attempting alternative removal methods for ${jobName}...`);

        // Try to find and remove by getting all repeatable jobs
        try {
          const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
          const targetJob = repeatableJobs.find(job => job.name === jobName);

          if (targetJob) {
            // Try removing with the exact job details
            const alternativeRemoved = await this.autoActionQueue.removeRepeatable(
              targetJob.name,
              {
                pattern: targetJob.pattern || '*/30 * * * *',
                tz: targetJob.tz || undefined,
              },
              targetJob.id || undefined
            );
            this.logger.log(`Alternative removal for ${jobName}: ${alternativeRemoved}`);
            removed = alternativeRemoved;
          }
        } catch (altError) {
          this.logger.warn(`Alternative removal failed for ${jobName}: ${altError.message}`);
        }
      }

      // Remove any non-repeatable jobs with this name (like retry jobs)
      // But skip repeatable job instances as they're managed by the scheduler
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      let removedJobs = 0;

      for (const job of jobs) {
        try {
          if (job.name === jobName && !job.opts?.repeat) {
            // Only remove non-repeatable jobs
            await job.remove();
            removedJobs++;
          } else if (job.name.startsWith(`${jobName}_retry_`)) {
            // Remove retry jobs for this auto action
            await job.remove();
            removedJobs++;
          }
        } catch (jobRemovalError) {
          // Log but don't fail if individual job removal fails
          if (jobRemovalError.message?.includes('belongs to a job scheduler')) {
            this.logger.warn(`Skipped removing scheduler-managed job: ${job.name}`);
          } else {
            this.logger.error(`Failed to remove individual job ${job.name}: ${jobRemovalError.message}`);
          }
        }
      }

      this.logger.log(`Removed auto action job: ${jobName} (pattern removed: ${removed}, individual jobs removed: ${removedJobs})`);

      // If we still couldn't remove the repeatable pattern, log a warning but don't fail
      if (!removed) {
        this.logger.warn(
          `Could not remove repeatable pattern for ${jobName}. ` +
          `This might be because the job is currently executing. ` +
          `The job should stop after the current execution completes.`
        );
      }
    } catch (error) {
      this.logger.error(`Failed to remove auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Get all active auto action jobs
   */
  async getActiveJobs(): Promise<Array<{ userId: number; targetId: string; type: AutoMode }>> {
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      return repeatableJobs.map(job => {
        const [userId, targetId, type] = job.name.split('_');
        return {
          userId: parseInt(userId),
          targetId,
          type: type as AutoMode,
        };
      });
    } catch (error) {
      this.logger.error('Failed to get active jobs', error);
      return [];
    }
  }

  /**
   * Get all active auto action jobs with user validation
   * This method checks if the jobs match the user's current auto mode settings
   */
  async getActiveJobsWithValidation(userService: any): Promise<Array<{
    userId: number;
    targetId: string;
    type: AutoMode;
    isValid: boolean;
    userAutoMode?: AutoMode;
    userTargetId?: string;
  }>> {
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const jobsWithValidation: Array<{
        userId: number;
        targetId: string;
        type: AutoMode;
        isValid: boolean;
        userAutoMode?: AutoMode;
        userTargetId?: string;
      }> = [];

      for (const job of repeatableJobs) {
        const [userId, targetId, type] = job.name.split('_');
        const parsedUserId = parseInt(userId);

        // Get user's current auto mode settings
        const user = await userService.findOne(parsedUserId);
        const isValid = user &&
          user.activeAutoMode === type &&
          user.autoTargetId === targetId;

        jobsWithValidation.push({
          userId: parsedUserId,
          targetId,
          type: type as AutoMode,
          isValid: !!isValid,
          userAutoMode: user?.activeAutoMode,
          userTargetId: user?.autoTargetId,
        });

        // If job is invalid, log and optionally remove it
        if (!isValid) {
          this.logger.warn(
            `Found inconsistent job: ${job.name}. ` +
            `User auto mode: ${user?.activeAutoMode}, target: ${user?.autoTargetId}. ` +
            `Job will be marked for cleanup.`
          );
        }
      }

      return jobsWithValidation;
    } catch (error) {
      this.logger.error('Failed to get active jobs with validation', error);
      return [];
    }
  }

  /**
   * Clean up inconsistent jobs that don't match user's current auto mode settings
   */
  async cleanupInconsistentJobs(userService: any): Promise<{
    totalJobs: number;
    inconsistentJobs: number;
    cleanedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      inconsistentJobs: 0,
      cleanedJobs: 0,
      errors: [] as string[],
    };

    try {
      const jobsWithValidation = await this.getActiveJobsWithValidation(userService);
      result.totalJobs = jobsWithValidation.length;

      const inconsistentJobs = jobsWithValidation.filter(job => !job.isValid);
      result.inconsistentJobs = inconsistentJobs.length;

      this.logger.log(
        `Found ${result.inconsistentJobs} inconsistent jobs out of ${result.totalJobs} total jobs`
      );

      for (const job of inconsistentJobs) {
        try {
          await this.removeAutoActionJob(job.userId, job.targetId, job.type);
          result.cleanedJobs++;
          this.logger.log(
            `Cleaned up inconsistent job: ${job.userId}_${job.targetId}_${job.type} ` +
            `(user mode: ${job.userAutoMode}, user target: ${job.userTargetId})`
          );
        } catch (error) {
          const errorMsg = `Failed to clean up job ${job.userId}_${job.targetId}_${job.type}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Cleanup completed: ${result.cleanedJobs}/${result.inconsistentJobs} inconsistent jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup inconsistent jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Check if a specific auto action job exists
   */
  async jobExists(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<boolean> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // Check repeatable jobs
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const repeatableExists = repeatableJobs.some(job => job.name === jobName);

      if (repeatableExists) {
        this.logger.log(`Found existing repeatable job: ${jobName}`);
        return true;
      }

      // Also check for any pending jobs with this name
      const pendingJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      const pendingExists = pendingJobs.some(job => job.name === jobName);

      if (pendingExists) {
        this.logger.log(`Found existing pending job: ${jobName}`);
        return true;
      }

      // Add a small delay and double-check to handle race conditions
      await new Promise(resolve => setTimeout(resolve, 100));

      // Final check for repeatable jobs
      const finalRepeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const finalExists = finalRepeatableJobs.some(job => job.name === jobName);

      if (finalExists) {
        this.logger.log(`Found existing repeatable job on final check: ${jobName}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to check if job exists: ${jobName}`, error);
      return false;
    }
  }

  /**
   * Force remove a job by trying all possible removal methods
   * This is a more aggressive approach for stuck jobs
   */
  async forceRemoveAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<{ success: boolean; methods: string[] }> {
    const jobName = `${userId}_${targetId}_${type}`;
    const result = { success: false, methods: [] as string[] };

    this.logger.log(`Force removing job: ${jobName}`);

    try {
      // Method 1: Regular removal
      try {
        await this.removeAutoActionJob(userId, targetId, type);
        result.methods.push('regular_removal');
        result.success = true;
        return result;
      } catch (error) {
        this.logger.warn(`Regular removal failed for ${jobName}: ${error.message}`);
      }

      // Method 2: Remove all repeatable jobs with this name pattern
      try {
        const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
        for (const job of repeatableJobs) {
          if (job.name === jobName) {
            const removed = await this.autoActionQueue.removeRepeatable(
              job.name,
              { pattern: job.pattern || '*/30 * * * *' },
              job.id || undefined
            );
            if (removed) {
              result.methods.push('pattern_removal');
              result.success = true;
            }
          }
        }
      } catch (error) {
        this.logger.warn(`Pattern removal failed for ${jobName}: ${error.message}`);
      }

      // Method 3: Remove all jobs (waiting, delayed, active) with this name
      try {
        const allJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active', 'completed', 'failed']);
        let removedCount = 0;
        for (const job of allJobs) {
          if (job.name === jobName || job.name.startsWith(`${jobName}_retry_`)) {
            try {
              await job.remove();
              removedCount++;
            } catch (jobError) {
              // Ignore individual job removal errors
            }
          }
        }
        if (removedCount > 0) {
          result.methods.push(`individual_jobs_${removedCount}`);
          result.success = true;
        }
      } catch (error) {
        this.logger.warn(`Individual job removal failed for ${jobName}: ${error.message}`);
      }

      this.logger.log(`Force removal result for ${jobName}: success=${result.success}, methods=[${result.methods.join(', ')}]`);
      return result;
    } catch (error) {
      this.logger.error(`Force removal failed for ${jobName}`, error);
      return result;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    try {
      const waiting = await this.autoActionQueue.getWaiting();
      const active = await this.autoActionQueue.getActive();
      const completed = await this.autoActionQueue.getCompleted();
      const failed = await this.autoActionQueue.getFailed();
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        repeatable: repeatableJobs.length,
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        repeatable: 0,
      };
    }
  }
}
