import { BadRequestException, Injectable, Logger, OnApplicationBootstrap, Inject, forwardRef } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionQueue } from '../queue/auto-action.queue';
import { WarStatus } from '../war/entity/war.entity';

@Injectable()
export class BullMQAutoActionService implements OnApplicationBootstrap {
  private readonly MINIMUM_ENERGY_THRESHOLD = 10;
  private readonly logger = new Logger(BullMQAutoActionService.name);

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    @Inject(forwardRef(() => AutoActionQueue))
    private readonly autoActionQueue: AutoActionQueue,
    private readonly moduleRef: ModuleRef,
  ) {}

  /**
   * Called after the application has fully started.
   * This is where we restore auto actions that were active before server restart.
   */
  async onApplicationBootstrap() {
    this.logger.log('BullMQAutoActionService bootstrap - restoring active auto actions...');

    try {
      // First, clean up any inconsistent jobs
      this.logger.log('Cleaning up inconsistent jobs before restoration...');
      const cleanupResult = await this.cleanupInconsistentJobs();

      if (cleanupResult.inconsistentJobs > 0) {
        this.logger.log(
          `Cleaned up ${cleanupResult.cleanedJobs}/${cleanupResult.inconsistentJobs} inconsistent jobs`
        );
      }

      // Then restore valid auto actions
      await this.restoreAutoActions();
      this.logger.log('Auto actions restoration completed');
    } catch (error) {
      this.logger.error(`Error during auto actions restoration: ${error.message}`, error.stack);
    }
  }

  /**
   * Start an auto action for a user
   */
  async startAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    // Validate input parameters
    if (!userId || !targetId || !type) {
      throw new BadRequestException('Invalid parameters: userId, targetId, and type are required');
    }

    if (type === AutoMode.NONE) {
      throw new BadRequestException('Cannot start auto action with NONE type');
    }

    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.isPremium) {
      throw new BadRequestException('Auto mode is only available for premium users');
    }

    // Check if user already has an active auto mode
    if (user.activeAutoMode !== AutoMode.NONE) {
      throw new BadRequestException(
        `User already has active auto mode: ${user.activeAutoMode}. Stop current auto mode before starting a new one.`
      );
    }

    // Check if job already exists (double-check)
    const jobExists = await this.autoActionQueue.jobExists(userId, targetId, type);
    if (jobExists) {
      throw new BadRequestException(`Auto action already running for this ${type}`);
    }

    // Set expiration time - 24 hours for work, null for war (war has its own end time)
    let expiresAt: Date | null = null;
    if (type === AutoMode.WORK) {
      expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
    }

    try {
      // Update user with auto mode info
      await this.userService.updateAutoMode(userId, {
        activeAutoMode: type,
        autoTargetId: targetId,
        autoModeExpiresAt: expiresAt,
      });

      // Add job to queue
      await this.autoActionQueue.addAutoActionJob(userId, targetId, type);

      // Validate the action target (basic validation)
      await this.validateActionTarget(userId, targetId, type);

      // Execute immediate validation
      await this.executeImmediateAction(userId, targetId, type);

      this.logger.log(`Started auto ${type} for user ${userId} on target ${targetId} (expires: ${expiresAt || 'never'})`);
    } catch (error) {
      // Rollback on error
      this.logger.error(`Failed to start auto ${type} for user ${userId}: ${error.message}`);

      try {
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
      } catch (rollbackError) {
        this.logger.error(`Failed to rollback auto action start: ${rollbackError.message}`);
      }

      throw error;
    }
  }

  /**
   * Stop an auto action for a user
   */
  async stopAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    try {
      // Remove job from queue
      await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);

      // Update user to clear auto mode
      await this.userService.updateAutoMode(userId, {
        activeAutoMode: AutoMode.NONE,
        autoTargetId: null,
        autoModeExpiresAt: null,
      });

      // Add a small delay to ensure the job removal is fully processed
      await new Promise(resolve => setTimeout(resolve, 500));

      this.logger.log(`Stopped auto ${type} for user ${userId} on target ${targetId}`);
    } catch (error) {
      this.logger.error(`Failed to stop auto ${type} for user ${userId}: ${error.message}`);

      // Even if job removal fails, try to clear the user's auto mode
      try {
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        this.logger.log(`Cleared user auto mode for user ${userId} despite job removal failure`);
      } catch (userUpdateError) {
        this.logger.error(`Failed to clear user auto mode for user ${userId}: ${userUpdateError.message}`);
      }

      throw error;
    }
  }

  /**
   * Validate the action target (comprehensive validation)
   */
  private async validateActionTarget(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    try {
      if (type === AutoMode.WORK) {
        // Basic validation for factory - check if it's a valid number
        const factoryId = parseInt(targetId);
        if (isNaN(factoryId) || factoryId <= 0) {
          throw new BadRequestException('Invalid factory ID');
        }

        // Dynamically resolve FactoryService to avoid circular dependency
        const { FactoryService } = await import('../factory/factory.service');
        const factoryService = this.moduleRef.get(FactoryService, { strict: false });

        if (!factoryService) {
          throw new BadRequestException('FactoryService not available');
        }

        // Check if factory exists
        const factory = await factoryService.findOne(factoryId);
        if (!factory) {
          throw new BadRequestException(`Factory with ID ${factoryId} not found`);
        }

        // Check if factory has reached maximum worker capacity
        const currentWorkerCount = await factoryService.getWorkerCount(factoryId);
        if (currentWorkerCount >= factory.maxWorkers) {
          throw new BadRequestException(
            `Factory has reached maximum capacity of ${factory.maxWorkers} workers (current: ${currentWorkerCount})`
          );
        }

        this.logger.log(
          `Factory validation passed for factory ${factoryId}: ${currentWorkerCount}/${factory.maxWorkers} workers`
        );

      } else if (type === AutoMode.WAR) {
        // Basic validation for war - check if it's a valid UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(targetId)) {
          throw new BadRequestException('Invalid war ID format');
        }

        // Dynamically resolve WarService to avoid circular dependency
        const { WarService } = await import('../war/war.service');
        const warService = this.moduleRef.get(WarService, { strict: false });

        if (!warService) {
          throw new BadRequestException('WarService not available');
        }

        // Check if war exists
        const war = await warService.findWarById(targetId);
        if (!war) {
          throw new BadRequestException(`War with ID ${targetId} not found`);
        }

        // Check if war is not ended or pending (only GROUND_PHASE is valid for auto attacks)
        if (war.status === WarStatus.ENDED) {
          throw new BadRequestException(`War ${targetId} has already ended`);
        }

        if (war.status === WarStatus.PENDING) {
          throw new BadRequestException(`War ${targetId} is still pending and has not started yet`);
        }

        this.logger.log(
          `War validation passed for war ${targetId}: status is ${war.status}`
        );
      }
    } catch (error) {
      this.logger.error(`Target validation failed for user ${userId}, target ${targetId}, type ${type}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute an action immediately when auto mode is started
   */
  private async executeImmediateAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    try {
      // Get the user without updating energy first
      const currentUser = await this.userService.findOne(userId);

      // Calculate current energy without saving
      const energyBefore = this.energyService.calculateCurrentEnergy(currentUser);

      // Check if user has minimum required energy
      if (energyBefore >= this.MINIMUM_ENERGY_THRESHOLD) {
        this.logger.log(
          `Executing immediate auto ${type} for user ${userId} on target ${targetId} with ${energyBefore} energy`,
        );

        // Execute the action immediately
        let actionExecuted = false;
        if (type === AutoMode.WORK) {
          actionExecuted = await this.executeAutoWork(userId, targetId, energyBefore);
        } else if (type === AutoMode.WAR) {
          actionExecuted = await this.executeAutoWar(userId, targetId, energyBefore);
        }

        if (actionExecuted) {
          // Check if energy was consumed and schedule retry if needed
          await this.checkEnergyConsumptionAndScheduleRetry(userId, targetId, type, energyBefore);
        }

        this.logger.log(`Immediate auto ${type} execution completed for user ${userId}`);
      } else {
        this.logger.log(
          `Skipping immediate auto ${type} for user ${userId} on target ${targetId} - insufficient energy (${energyBefore}, minimum required: ${this.MINIMUM_ENERGY_THRESHOLD})`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Immediate auto ${type} execution failed for user ${userId} on target ${targetId}: ${error.message}`,
      );
      // Don't throw the error - we don't want to prevent auto mode from starting if immediate execution fails
      // The scheduled jobs will handle subsequent executions
    }
  }

  /**
   * Execute auto work action
   */
  private async executeAutoWork(userId: number, factoryId: string, energyAmount: number): Promise<boolean> {
    try {
      // Dynamically resolve FactoryService to avoid circular dependency
      const { FactoryService } = await import('../factory/factory.service');
      const factoryService = this.moduleRef.get(FactoryService, { strict: false });

      if (!factoryService) {
        throw new Error('FactoryService not available');
      }

      // Work at the factory
      await factoryService.workAtFactory(+factoryId, userId, {
        energySpent: energyAmount,
      });

      this.logger.log(`Auto work successful for user ${userId} at factory ${factoryId}`);
      return true; // Action was executed successfully
    } catch (error) {
      this.logger.error(`Auto work failed for user ${userId} at factory ${factoryId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute auto war action
   */
  private async executeAutoWar(userId: number, warId: string, energyAmount: number): Promise<boolean> {
    try {
      // Dynamically resolve WarService to avoid circular dependency
      const { WarService } = await import('../war/war.service');
      const warService = this.moduleRef.get(WarService, { strict: false });

      if (!warService) {
        throw new Error('WarService not available');
      }

      // Check if war is still active (GROUND_PHASE is the active phase)
      const war = await warService.findWarById(warId);
      if (!war || war.status !== WarStatus.GROUND_PHASE) {
        this.logger.log(`War ${warId} is no longer active (status: ${war?.status}), cannot execute auto attack for user ${userId}`);
        return false; // War ended, no action executed
      }

      // Create participation DTO for the war service
      const participateDto = {
        energyAmount: energyAmount,
        autoMode: false, // Don't trigger auto mode recursively
        autoEnergyPercentage: 100,
      };

      // Participate in the war (correct parameter order: userId, warId, dto)
      await warService.participateInWar(userId, warId, participateDto);

      this.logger.log(`Auto war attack successful for user ${userId} in war ${warId}`);
      return true; // Action was executed successfully
    } catch (error) {
      this.logger.error(`Auto war attack failed for user ${userId} in war ${warId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if energy was consumed and schedule retry if needed
   */
  private async checkEnergyConsumptionAndScheduleRetry(
    userId: number,
    targetId: string,
    type: AutoMode,
    energyBefore: number,
  ): Promise<void> {
    try {
      // Wait a moment for the database to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get updated user data
      const updatedUser = await this.userService.findOne(userId);
      const energyAfter = this.energyService.calculateCurrentEnergy(updatedUser);

      this.logger.log(`Energy check for user ${userId}: before=${energyBefore}, after=${energyAfter}`);

      // Check if energy was actually consumed
      if (energyAfter >= energyBefore) {
        this.logger.warn(`Energy was not consumed for user ${userId} (before: ${energyBefore}, after: ${energyAfter}). Scheduling retry...`);

        // Schedule a retry in 2 minutes instead of waiting for the next 30-minute cycle
        // This is only for error recovery, not continuous execution
        await this.autoActionQueue.addRetryJob(userId, targetId, type, 120000); // 2 minutes
      } else {
        this.logger.log(`Energy successfully consumed for user ${userId} (${energyBefore} -> ${energyAfter})`);
        // No additional retry needed - the regular 30-minute cycle will handle the next execution
        // This maintains the proper energy regeneration timing
      }
    } catch (error) {
      this.logger.error(`Error checking energy consumption for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Restore auto actions that were active before server restart
   */
  private async restoreAutoActions(): Promise<void> {
    try {
      // Find all users with active auto modes
      const usersWithAutoMode = await this.userService.findUsersWithActiveAutoMode();

      this.logger.log(`Found ${usersWithAutoMode.length} users with active auto modes`);

      for (const user of usersWithAutoMode) {
        try {
          // Check if user is still premium
          if (!user.isPremium) {
            this.logger.log(`User ${user.id} is no longer premium, clearing auto mode`);
            await this.userService.updateAutoMode(user.id, {
              activeAutoMode: AutoMode.NONE,
              autoTargetId: null,
              autoModeExpiresAt: null,
            });
            continue;
          }

          // Check if work auto mode has expired
          if (user.activeAutoMode === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
            this.logger.log(`Auto work for user ${user.id} has expired, clearing auto mode`);
            await this.userService.updateAutoMode(user.id, {
              activeAutoMode: AutoMode.NONE,
              autoTargetId: null,
              autoModeExpiresAt: null,
            });
            continue;
          }

          // Check if job already exists in queue
          const jobExists = await this.autoActionQueue.jobExists(
            user.id,
            user.autoTargetId,
            user.activeAutoMode,
          );

          if (!jobExists) {
            // Restore the job
            await this.autoActionQueue.addAutoActionJob(
              user.id,
              user.autoTargetId,
              user.activeAutoMode,
            );

            this.logger.log(
              `Restored auto ${user.activeAutoMode} for user ${user.id} on target ${user.autoTargetId}`,
            );
          } else {
            this.logger.log(
              `Auto ${user.activeAutoMode} job already exists for user ${user.id} on target ${user.autoTargetId}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Failed to restore auto action for user ${user.id}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Failed to restore auto actions: ${error.message}`);
    }
  }

  /**
   * Get statistics about active auto actions
   */
  async getAutoActionStats() {
    try {
      const queueStats = await this.autoActionQueue.getQueueStats();
      const activeJobs = await this.autoActionQueue.getActiveJobs();

      return {
        queueStats,
        activeJobs: activeJobs.length,
        jobsByType: {
          work: activeJobs.filter(job => job.type === AutoMode.WORK).length,
          war: activeJobs.filter(job => job.type === AutoMode.WAR).length,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get auto action stats', error);
      return {
        queueStats: { waiting: 0, active: 0, completed: 0, failed: 0, repeatable: 0 },
        activeJobs: 0,
        jobsByType: { work: 0, war: 0 },
      };
    }
  }

  /**
   * Get detailed statistics about active auto actions with validation
   */
  async getAutoActionStatsWithValidation() {
    try {
      const queueStats = await this.autoActionQueue.getQueueStats();
      const jobsWithValidation = await this.autoActionQueue.getActiveJobsWithValidation(this.userService);

      const validJobs = jobsWithValidation.filter(job => job.isValid);
      const invalidJobs = jobsWithValidation.filter(job => !job.isValid);

      return {
        queueStats,
        totalJobs: jobsWithValidation.length,
        validJobs: validJobs.length,
        invalidJobs: invalidJobs.length,
        jobsByType: {
          work: validJobs.filter(job => job.type === AutoMode.WORK).length,
          war: validJobs.filter(job => job.type === AutoMode.WAR).length,
        },
        invalidJobDetails: invalidJobs.map(job => ({
          userId: job.userId,
          targetId: job.targetId,
          jobType: job.type,
          userAutoMode: job.userAutoMode,
          userTargetId: job.userTargetId,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get auto action stats with validation', error);
      return {
        queueStats: { waiting: 0, active: 0, completed: 0, failed: 0, repeatable: 0 },
        totalJobs: 0,
        validJobs: 0,
        invalidJobs: 0,
        jobsByType: { work: 0, war: 0 },
        invalidJobDetails: [],
      };
    }
  }

  /**
   * Clean up inconsistent auto action jobs
   */
  async cleanupInconsistentJobs() {
    try {
      this.logger.log('Starting cleanup of inconsistent auto action jobs...');
      const result = await this.autoActionQueue.cleanupInconsistentJobs(this.userService);

      this.logger.log(
        `Cleanup completed: ${result.cleanedJobs}/${result.inconsistentJobs} inconsistent jobs removed ` +
        `(${result.totalJobs} total jobs checked)`
      );

      if (result.errors.length > 0) {
        this.logger.warn(`Cleanup had ${result.errors.length} errors: ${result.errors.join(', ')}`);
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup inconsistent jobs', error);
      return {
        totalJobs: 0,
        inconsistentJobs: 0,
        cleanedJobs: 0,
        errors: [`Cleanup failed: ${error.message}`],
      };
    }
  }

  /**
   * Force remove a stuck auto action job
   */
  async forceRemoveAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ) {
    try {
      this.logger.log(`Force removing auto ${type} for user ${userId} on target ${targetId}`);

      const result = await this.autoActionQueue.forceRemoveAutoActionJob(userId, targetId, type);

      // Also clear the user's auto mode regardless of job removal success
      await this.userService.updateAutoMode(userId, {
        activeAutoMode: AutoMode.NONE,
        autoTargetId: null,
        autoModeExpiresAt: null,
      });

      this.logger.log(
        `Force removal completed for user ${userId}: success=${result.success}, methods=[${result.methods.join(', ')}]`
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to force remove auto ${type} for user ${userId}: ${error.message}`);
      throw error;
    }
  }
}
